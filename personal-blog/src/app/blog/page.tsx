import type { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: '博客',
  description: '殷浩玮的技术博客，分享前端开发、数据分析和人工智能相关的文章',
};

// 模拟博客数据
const blogPosts = [
  {
    id: 'nextjs-15-features',
    title: 'Next.js 15 新特性深度解析',
    excerpt: '探索 Next.js 15 带来的革命性变化，包括 App Router 的优化、服务端组件的增强、Turbopack 的性能提升等关键特性。',
    date: '2024-01-15',
    category: '技术分享',
    readTime: '8 分钟',
    tags: ['Next.js', 'React', 'Web开发']
  },
  {
    id: 'typescript-best-practices',
    title: 'TypeScript 最佳实践指南',
    excerpt: '分享在实际项目中总结的 TypeScript 最佳实践，包括类型定义、错误处理、性能优化等方面的经验。',
    date: '2024-01-10',
    category: '学习笔记',
    readTime: '12 分钟',
    tags: ['TypeScript', '最佳实践', '前端开发']
  },
  {
    id: 'data-visualization-with-d3',
    title: 'D3.js 数据可视化实战',
    excerpt: '从零开始学习 D3.js，通过实际案例展示如何创建交互式数据可视化图表，提升数据表达能力。',
    date: '2024-01-05',
    category: '数据分析',
    readTime: '15 分钟',
    tags: ['D3.js', '数据可视化', 'JavaScript']
  },
  {
    id: 'machine-learning-basics',
    title: '机器学习入门指南',
    excerpt: '机器学习基础概念介绍，包括监督学习、无监督学习、深度学习等核心概念，以及 Python 实践示例。',
    date: '2024-01-01',
    category: '人工智能',
    readTime: '20 分钟',
    tags: ['机器学习', 'Python', 'AI']
  },
  {
    id: 'react-performance-optimization',
    title: 'React 性能优化实战技巧',
    excerpt: '深入探讨 React 应用的性能优化策略，包括组件优化、状态管理、代码分割等实用技巧。',
    date: '2023-12-28',
    category: '技术分享',
    readTime: '10 分钟',
    tags: ['React', '性能优化', '前端开发']
  },
  {
    id: 'python-data-analysis',
    title: 'Python 数据分析工具链',
    excerpt: '介绍 Python 生态系统中的数据分析工具，包括 Pandas、NumPy、Matplotlib 等库的使用方法和最佳实践。',
    date: '2023-12-25',
    category: '数据分析',
    readTime: '18 分钟',
    tags: ['Python', 'Pandas', '数据分析']
  }
];

const categories = ['全部', '技术分享', '学习笔记', '数据分析', '人工智能'];

export default function Blog() {
  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="text-center mb-16">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          我的博客
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300">
          分享技术见解、学习心得和实践经验
        </p>
      </div>

      {/* Categories Filter */}
      <div className="flex flex-wrap justify-center gap-4 mb-12">
        {categories.map((category) => (
          <button
            key={category}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              category === '全部'
                ? 'bg-blue-600 text-white'
                : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
            }`}
          >
            {category}
          </button>
        ))}
      </div>

      {/* Blog Posts Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {blogPosts.map((post) => (
          <article
            key={post.id}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow"
          >
            <div className="p-6">
              {/* Category and Date */}
              <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-3">
                <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
                  {post.category}
                </span>
                <time>{post.date}</time>
              </div>

              {/* Title */}
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                <Link
                  href={`/blog/${post.id}`}
                  className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                >
                  {post.title}
                </Link>
              </h2>

              {/* Excerpt */}
              <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                {post.excerpt}
              </p>

              {/* Tags */}
              <div className="flex flex-wrap gap-2 mb-4">
                {post.tags.map((tag) => (
                  <span
                    key={tag}
                    className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>

              {/* Read More */}
              <div className="flex items-center justify-between">
                <Link
                  href={`/blog/${post.id}`}
                  className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium text-sm"
                >
                  阅读全文 →
                </Link>
                <span className="text-gray-500 dark:text-gray-400 text-sm">
                  {post.readTime}
                </span>
              </div>
            </div>
          </article>
        ))}
      </div>

      {/* Load More */}
      <div className="text-center mt-12">
        <button className="px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
          加载更多文章
        </button>
      </div>
    </div>
  );
} 