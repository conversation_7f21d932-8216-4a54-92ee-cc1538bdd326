import Link from 'next/link';
import { ArrowRightIcon } from '@heroicons/react/24/outline';

export default function Home() {
  return (
    <div className="max-w-4xl mx-auto">
      {/* Hero Section */}
      <section className="text-center py-20">
        <div className="mb-8">
          <h1 className="text-5xl font-bold text-gray-900 dark:text-white mb-4">
            你好，我是
            <span className="text-blue-600 dark:text-blue-400"> 殷浩玮</span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            一名热爱技术的开发者，专注于前端开发、数据分析和人工智能。
            在这里分享我的学习心得、项目经验和生活感悟。
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link
            href="/about"
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
          >
            了解更多
            <ArrowRightIcon className="ml-2 h-5 w-5" />
          </Link>
          <Link
            href="/blog"
            className="inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            查看博客
          </Link>
        </div>
      </section>

      {/* Skills Section */}
      <section className="py-16">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center mb-12">
          技能专长
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="text-blue-600 dark:text-blue-400 text-2xl mb-4">🚀</div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              前端开发
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              熟练使用 React、Next.js、TypeScript 等现代前端技术栈
            </p>
            <div className="flex flex-wrap gap-2">
              <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-sm rounded-full">React</span>
              <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-sm rounded-full">Next.js</span>
              <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-sm rounded-full">TypeScript</span>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="text-green-600 dark:text-green-400 text-2xl mb-4">📊</div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              数据分析
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              运用 Python、SQL 等工具进行数据处理和分析
            </p>
            <div className="flex flex-wrap gap-2">
              <span className="px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-sm rounded-full">Python</span>
              <span className="px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-sm rounded-full">Pandas</span>
              <span className="px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-sm rounded-full">SQL</span>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="text-purple-600 dark:text-purple-400 text-2xl mb-4">🤖</div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              人工智能
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              探索机器学习和深度学习的应用与实践
            </p>
            <div className="flex flex-wrap gap-2">
              <span className="px-3 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-sm rounded-full">TensorFlow</span>
              <span className="px-3 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-sm rounded-full">PyTorch</span>
              <span className="px-3 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-sm rounded-full">Scikit-learn</span>
            </div>
          </div>
        </div>
      </section>

      {/* Latest Blog Posts */}
      <section className="py-16">
        <div className="flex justify-between items-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
            最新文章
          </h2>
          <Link
            href="/blog"
            className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
          >
            查看全部 →
          </Link>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <article className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-3">
              <time>2024-01-15</time>
              <span className="mx-2">•</span>
              <span>技术分享</span>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              <Link href="/blog/nextjs-15-features" className="hover:text-blue-600 dark:hover:text-blue-400">
                Next.js 15 新特性深度解析
              </Link>
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              探索 Next.js 15 带来的革命性变化，包括 App Router 的优化、服务端组件的增强等...
            </p>
            <Link
              href="/blog/nextjs-15-features"
              className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
            >
              阅读全文 →
            </Link>
          </article>

          <article className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-3">
              <time>2024-01-10</time>
              <span className="mx-2">•</span>
              <span>学习笔记</span>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              <Link href="/blog/typescript-best-practices" className="hover:text-blue-600 dark:hover:text-blue-400">
                TypeScript 最佳实践指南
              </Link>
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              分享在实际项目中总结的 TypeScript 最佳实践，帮助你写出更好的类型安全代码...
            </p>
            <Link
              href="/blog/typescript-best-practices"
              className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
            >
              阅读全文 →
            </Link>
          </article>
        </div>
      </section>
    </div>
  );
}
